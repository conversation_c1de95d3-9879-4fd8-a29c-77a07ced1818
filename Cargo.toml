[package]
name = "xlive"
version = "0.1.0"
authors = ["wida <<EMAIL>>"]
edition = "2018"

[dependencies]
bytes = { version = "1", features = ["serde"] }
rml_rtmp = "^0.3"
thiserror = "^1.0"
anyhow = "^1.0"
log = "^0.4"
serde = { version = "^1.0", features = ["derive"] }
serde_json = "^1.0"
futures = "0.3.5"
tokio-util = { version = "0.6.2", features = ["codec"] }
tokio-stream = { version = "0.1.2", features = ["time"] }
bincode = "^1.3"
env_logger = "0.11.0"
chrono="*"
redis = { version = "0.17.0", features = ["tokio-comp"]}
async-trait = "0.1.36"
tokio = { version = "1.14.0", features = ["full", "tracing"] }
hyper = { version = "0.14", features = ["stream", "server", "http1", "http2", "tcp", "client"],optional = true}
url = { version="2.3.1"}
mpeg2ts = { version = "0.1",optional = true}
lazy_static = { version = "1" , optional=true}
config = "0.12"


[dependencies.pic]
path = "pic"
optional = true


[features]
default = ["http-flv","hls","flv"]
auth=[] #开启用户认证，使用redis
flv=[] # 本地保存flv文件
http-flv=["hyper"]
keyframe_image=["pic"] # 关键帧截屏
hls=["mpeg2ts","lazy_static"]

[[bin]]
name = "xlive"
path = "bin/main.rs"