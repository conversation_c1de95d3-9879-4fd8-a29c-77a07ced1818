# CDN集成示例

本文档展示如何配置和使用直播服务的CDN功能。

## 配置CDN

### 1. 本地CDN（开发/测试）

```yaml
# conf.yaml
cdn:
  enabled: true
  provider:
    type: Local
    base_path: cdn_cache  # 本地CDN缓存路径
  upload_concurrency: 4  # 上传并发数
  retry_attempts: 3      # 重试次数
  retry_delay_secs: 1    # 重试间隔（秒）
  upload_timeout_secs: 30 # 上传超时（秒）
  local_cache: true      # 是否保留本地缓存
  cache_retention_secs: 3600 # 缓存保留时间（秒）
```

### 2. 阿里云CDN（生产环境）

```yaml
# conf.yaml
cdn:
  enabled: true
  provider:
    type: AliCloud
    access_key: "your_access_key"
    secret_key: "your_secret_key"
    bucket: "your-bucket-name"
    region: "oss-cn-hangzhou"
    domain: "https://your-cdn-domain.com"
  upload_concurrency: 8
  retry_attempts: 5
  retry_delay_secs: 2
  upload_timeout_secs: 60
  local_cache: false  # 生产环境不保留本地缓存
  cache_retention_secs: 7200
```

### 3. 腾讯云CDN

```yaml
# conf.yaml
cdn:
  enabled: true
  provider:
    type: TencentCloud
    secret_id: "your_secret_id"
    secret_key: "your_secret_key"
    bucket: "your-bucket-name"
    region: "ap-guangzhou"
    domain: "https://your-cdn-domain.com"
  upload_concurrency: 6
  retry_attempts: 3
  retry_delay_secs: 1
  upload_timeout_secs: 45
  local_cache: false
  cache_retention_secs: 3600
```

### 4. AWS CloudFront

```yaml
# conf.yaml
cdn:
  enabled: true
  provider:
    type: AWS
    access_key: "your_access_key"
    secret_key: "your_secret_key"
    bucket: "your-s3-bucket"
    region: "us-east-1"
    distribution_id: "your_distribution_id"
  upload_concurrency: 10
  retry_attempts: 3
  retry_delay_secs: 1
  upload_timeout_secs: 30
  local_cache: false
  cache_retention_secs: 3600
```

### 5. 自定义HTTP上传

```yaml
# conf.yaml
cdn:
  enabled: true
  provider:
    type: Custom
    upload_url: "https://your-upload-endpoint.com/upload"
    headers:
      Authorization: "Bearer your-token"
      Content-Type: "application/octet-stream"
    auth_token: "your-auth-token"
  upload_concurrency: 4
  retry_attempts: 3
  retry_delay_secs: 2
  upload_timeout_secs: 60
  local_cache: true
  cache_retention_secs: 1800
```

## 使用CDN

### 1. 启动服务器

```bash
# 启动直播服务器
./target/release/xlive

# 服务器启动日志会显示CDN状态
# [INFO] Initializing CDN service with provider: Local { base_path: "cdn_cache" }
# [INFO] Global CDN service initialized and started
```

### 2. 推送RTMP流

```bash
# 使用FFmpeg推送流
ffmpeg -i input.mp4 -c copy -f flv rtmp://localhost:1935/wida/wida
```

### 3. 播放HLS流

当CDN启用时，M3U8播放列表中的TS文件URL会自动指向CDN：

```bash
# 获取播放列表
curl http://localhost:3001/wida/wida.m3u8

# 输出示例（CDN启用）：
#EXTM3U
#EXT-X-VERSION:3
#EXT-X-TARGETDURATION:6
#EXT-X-MEDIA-SEQUENCE:1
#EXTINF:5.000
https://your-cdn-domain.com/wida/wida/**********.ts
#EXTINF:5.000
https://your-cdn-domain.com/wida/wida/**********.ts
#EXTINF:5.000
https://your-cdn-domain.com/wida/wida/**********.ts
```

### 4. 监控CDN状态

```bash
# 查看CDN统计信息
curl http://localhost:3001/cdn/stats

# 输出示例：
{
  "enabled": true,
  "total_uploads": 156,
  "successful_uploads": 154,
  "failed_uploads": 2,
  "success_rate": 98.7,
  "total_bytes": 15728640,
  "average_upload_time_ms": 245,
  "last_upload_ago_secs": 5
}
```

### 5. 手动触发上传

```bash
# 手动触发CDN上传队列处理
curl -X POST http://localhost:3001/cdn/upload
```

## CDN工作流程

### 1. TS文件生成
1. RTMP流接收并处理
2. 生成TS段文件到本地存储
3. 自动添加到CDN上传队列

### 2. CDN上传
1. CDN服务每5秒处理一次上传队列
2. 并发上传TS文件到CDN
3. 支持重试和错误处理
4. 记录上传统计信息

### 3. URL重写
1. 生成M3U8播放列表时检查CDN可用性
2. 如果CDN可用，使用CDN URL
3. 如果CDN不可用，回退到本地URL

## 性能优化建议

### 1. 上传并发数
- 本地CDN: 4-8个并发
- 云CDN: 8-16个并发
- 根据网络带宽调整

### 2. 重试策略
- 网络稳定: 3次重试
- 网络不稳定: 5-10次重试
- 重试间隔: 1-5秒

### 3. 缓存策略
- 开发环境: 保留本地缓存
- 生产环境: 不保留本地缓存
- 缓存时间: 1-2小时

### 4. 监控指标
- 上传成功率 > 95%
- 平均上传时间 < 1秒
- 队列积压 < 10个文件

## 故障排除

### 1. 上传失败
```bash
# 检查CDN配置
curl http://localhost:3001/cdn/stats

# 查看服务器日志
tail -f logs/xlive.log | grep CDN
```

### 2. 播放列表URL错误
- 检查CDN域名配置
- 验证CDN服务可用性
- 确认文件上传成功

### 3. 性能问题
- 调整上传并发数
- 增加重试次数
- 检查网络带宽

## 最佳实践

### 1. 生产环境配置
```yaml
cdn:
  enabled: true
  provider:
    type: AliCloud  # 或其他云提供商
    # ... 配置参数
  upload_concurrency: 8
  retry_attempts: 5
  retry_delay_secs: 2
  upload_timeout_secs: 60
  local_cache: false  # 节省存储空间
  cache_retention_secs: 7200
```

### 2. 监控和告警
- 监控上传成功率
- 设置失败率告警
- 监控队列积压情况

### 3. 安全考虑
- 使用HTTPS CDN域名
- 配置访问控制
- 定期轮换访问密钥

### 4. 成本优化
- 选择合适的CDN区域
- 配置缓存策略
- 监控流量使用

## API参考

### CDN统计端点
```
GET /cdn/stats
```

返回CDN上传统计信息。

### 手动上传触发
```
POST /cdn/upload
```

手动触发CDN上传队列处理。

### 流列表
```
GET /streams
```

查看当前活跃的流列表。

## 环境变量

可以通过环境变量覆盖CDN配置：

```bash
export XLIVE_CDN_ENABLED=true
export XLIVE_CDN_PROVIDER_TYPE=Local
export XLIVE_CDN_UPLOAD_CONCURRENCY=8
export XLIVE_CDN_RETRY_ATTEMPTS=5
```

## 日志示例

```
[INFO] Initializing CDN service with provider: Local { base_path: "cdn_cache" }
[INFO] Global CDN service initialized and started
[INFO] Starting CDN service with 5s processing interval
[INFO] Processing 3 CDN upload tasks
[INFO] Successfully uploaded data/wida/wida/**********.ts to CDN: http://localhost:8080/wida/wida/**********.ts (245ms, 1048576 bytes)
[DEBUG] CDN Stats - Total: 156, Success: 154, Failed: 2, Avg time: 245ms
```
