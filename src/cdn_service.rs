use std::sync::Arc;
use std::time::Duration;
use tokio::time::interval;
use anyhow::Result;
use crate::cdn::{CdnManager, get_global_cdn_manager, init_global_cdn_manager};
use crate::config::Settings;

/// CDN服务，负责定期处理上传队列
pub struct CdnService {
    manager: Arc<CdnManager>,
    process_interval: Duration,
}

impl CdnService {
    pub fn new(manager: Arc<CdnManager>) -> Self {
        Self {
            manager,
            process_interval: Duration::from_secs(5), // 每5秒处理一次队列
        }
    }

    /// 从配置初始化CDN服务
    pub fn from_config(settings: &Settings) -> Result<Option<Self>> {
        if let Some(cdn_config) = &settings.cdn {
            if cdn_config.enabled {
                log::info!("Initializing CDN service with provider: {:?}", cdn_config.provider);
                
                // 初始化全局CDN管理器
                init_global_cdn_manager(cdn_config.clone())?;
                
                if let Some(manager) = get_global_cdn_manager() {
                    return Ok(Some(Self::new(manager)));
                }
            } else {
                log::info!("CDN service is disabled in configuration");
            }
        } else {
            log::info!("No CDN configuration found, CDN service disabled");
        }
        
        Ok(None)
    }

    /// 启动CDN服务
    pub async fn run(&self) -> Result<()> {
        log::info!("Starting CDN service with {}s processing interval", 
                  self.process_interval.as_secs());
        
        let mut interval = interval(self.process_interval);
        
        loop {
            interval.tick().await;
            
            // 处理上传队列
            if let Err(e) = self.manager.process_upload_queue().await {
                log::error!("Failed to process CDN upload queue: {}", e);
            }
            
            // 定期打印统计信息
            let stats = self.manager.get_upload_stats().await;
            if stats.total_uploads > 0 {
                log::debug!(
                    "CDN Stats - Total: {}, Success: {}, Failed: {}, Avg time: {}ms",
                    stats.total_uploads,
                    stats.successful_uploads,
                    stats.failed_uploads,
                    stats.average_upload_time.as_millis()
                );
            }
        }
    }

    /// 获取上传统计信息
    pub async fn get_stats(&self) -> crate::cdn::UploadStats {
        self.manager.get_upload_stats().await
    }

    /// 手动触发队列处理
    pub async fn process_queue(&self) -> Result<()> {
        self.manager.process_upload_queue().await
    }

    /// 获取CDN URL
    pub fn get_cdn_url(&self, remote_path: &str) -> String {
        self.manager.get_cdn_url(remote_path)
    }
}

/// 全局CDN服务实例
use std::sync::OnceLock;
static GLOBAL_CDN_SERVICE: OnceLock<Arc<CdnService>> = OnceLock::new();

/// 初始化全局CDN服务
pub fn init_global_cdn_service(settings: &Settings) -> Result<()> {
    if let Some(service) = CdnService::from_config(settings)? {
        let service_arc = Arc::new(service);
        GLOBAL_CDN_SERVICE.set(service_arc.clone())
            .map_err(|_| anyhow::anyhow!("CDN service already initialized"))?;
        
        // 启动CDN服务任务
        let service_clone = service_arc.clone();
        tokio::spawn(async move {
            if let Err(e) = service_clone.run().await {
                log::error!("CDN service error: {}", e);
            }
        });
        
        log::info!("Global CDN service initialized and started");
    }
    
    Ok(())
}

/// 获取全局CDN服务
pub fn get_global_cdn_service() -> Option<Arc<CdnService>> {
    GLOBAL_CDN_SERVICE.get().cloned()
}

/// CDN统计信息的JSON序列化版本
#[derive(serde::Serialize)]
pub struct CdnStatsResponse {
    pub enabled: bool,
    pub total_uploads: u64,
    pub successful_uploads: u64,
    pub failed_uploads: u64,
    pub success_rate: f64,
    pub total_bytes: u64,
    pub average_upload_time_ms: u64,
    pub last_upload_ago_secs: Option<u64>,
}

impl From<crate::cdn::UploadStats> for CdnStatsResponse {
    fn from(stats: crate::cdn::UploadStats) -> Self {
        let success_rate = if stats.total_uploads > 0 {
            (stats.successful_uploads as f64 / stats.total_uploads as f64) * 100.0
        } else {
            0.0
        };
        
        let last_upload_ago_secs = stats.last_upload_time
            .map(|t| t.elapsed().as_secs());
        
        Self {
            enabled: true,
            total_uploads: stats.total_uploads,
            successful_uploads: stats.successful_uploads,
            failed_uploads: stats.failed_uploads,
            success_rate,
            total_bytes: stats.total_bytes,
            average_upload_time_ms: stats.average_upload_time.as_millis() as u64,
            last_upload_ago_secs,
        }
    }
}

/// 获取CDN统计信息的便利函数
pub async fn get_cdn_stats() -> CdnStatsResponse {
    if let Some(service) = get_global_cdn_service() {
        let stats = service.get_stats().await;
        CdnStatsResponse::from(stats)
    } else {
        CdnStatsResponse {
            enabled: false,
            total_uploads: 0,
            successful_uploads: 0,
            failed_uploads: 0,
            success_rate: 0.0,
            total_bytes: 0,
            average_upload_time_ms: 0,
            last_upload_ago_secs: None,
        }
    }
}

/// 手动触发CDN队列处理的便利函数
pub async fn trigger_cdn_upload() -> Result<()> {
    if let Some(service) = get_global_cdn_service() {
        service.process_queue().await
    } else {
        Err(anyhow::anyhow!("CDN service not available"))
    }
}

/// 获取文件的CDN URL
pub fn get_file_cdn_url(remote_path: &str) -> Option<String> {
    get_global_cdn_service().map(|service| service.get_cdn_url(remote_path))
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::cdn::{CdnConfig, CdnProviderConfig};

    #[test]
    fn test_cdn_stats_response() {
        let stats = crate::cdn::UploadStats {
            total_uploads: 100,
            successful_uploads: 95,
            failed_uploads: 5,
            total_bytes: 1024 * 1024, // 1MB
            average_upload_time: Duration::from_millis(500),
            last_upload_time: Some(std::time::Instant::now()),
        };
        
        let response = CdnStatsResponse::from(stats);
        assert_eq!(response.total_uploads, 100);
        assert_eq!(response.successful_uploads, 95);
        assert_eq!(response.failed_uploads, 5);
        assert_eq!(response.success_rate, 95.0);
        assert_eq!(response.total_bytes, 1024 * 1024);
        assert_eq!(response.average_upload_time_ms, 500);
        assert!(response.last_upload_ago_secs.is_some());
    }

    #[tokio::test]
    async fn test_cdn_service_disabled() {
        let settings = Settings {
            cdn: Some(CdnConfig {
                enabled: false,
                ..Default::default()
            }),
            ..Default::default()
        };
        
        let service = CdnService::from_config(&settings).unwrap();
        assert!(service.is_none());
    }

    #[tokio::test]
    async fn test_cdn_service_no_config() {
        let settings = Settings {
            cdn: None,
            ..Default::default()
        };
        
        let service = CdnService::from_config(&settings).unwrap();
        assert!(service.is_none());
    }

    #[test]
    fn test_disabled_cdn_stats() {
        let stats = CdnStatsResponse {
            enabled: false,
            total_uploads: 0,
            successful_uploads: 0,
            failed_uploads: 0,
            success_rate: 0.0,
            total_bytes: 0,
            average_upload_time_ms: 0,
            last_upload_ago_secs: None,
        };
        
        assert!(!stats.enabled);
        assert_eq!(stats.total_uploads, 0);
        assert_eq!(stats.success_rate, 0.0);
    }
}
