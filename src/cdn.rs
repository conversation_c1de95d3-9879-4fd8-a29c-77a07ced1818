use std::collections::HashMap;
use std::path::Path;
use std::sync::Arc;
use std::time::Duration;
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use tokio::fs;
use tokio::sync::RwLock;
use anyhow::{Result, anyhow};

/// CDN配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CdnConfig {
    /// CDN提供商类型
    pub provider: CdnProviderConfig,
    /// 是否启用CDN
    pub enabled: bool,
    /// 上传并发数
    pub upload_concurrency: usize,
    /// 重试次数
    pub retry_attempts: u32,
    /// 重试间隔（秒）
    pub retry_delay_secs: u64,
    /// 上传超时（秒）
    pub upload_timeout_secs: u64,
    /// 是否启用本地缓存
    pub local_cache: bool,
    /// 缓存保留时间（秒）
    pub cache_retention_secs: u64,
}

impl Default for CdnConfig {
    fn default() -> Self {
        Self {
            provider: CdnProviderConfig::Local {
                base_path: "cdn_cache".to_string(),
            },
            enabled: false,
            upload_concurrency: 4,
            retry_attempts: 3,
            retry_delay_secs: 1,
            upload_timeout_secs: 30,
            local_cache: true,
            cache_retention_secs: 3600, // 1小时
        }
    }
}

/// CDN提供商配置
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum CdnProviderConfig {
    /// 本地存储（开发/测试用）
    Local {
        base_path: String,
    },
    /// 阿里云CDN
    AliCloud {
        access_key: String,
        secret_key: String,
        bucket: String,
        region: String,
        domain: String,
    },
    /// 腾讯云CDN
    TencentCloud {
        secret_id: String,
        secret_key: String,
        bucket: String,
        region: String,
        domain: String,
    },
    /// AWS CloudFront
    AWS {
        access_key: String,
        secret_key: String,
        bucket: String,
        region: String,
        distribution_id: String,
    },
    /// 自定义HTTP上传
    Custom {
        upload_url: String,
        headers: HashMap<String, String>,
        auth_token: Option<String>,
    },
}

impl Default for CdnProviderConfig {
    fn default() -> Self {
        Self::Local {
            base_path: "cdn_cache".to_string(),
        }
    }
}

/// CDN上传结果
#[derive(Debug, Clone)]
pub struct UploadResult {
    /// 文件的CDN URL
    pub cdn_url: String,
    /// 上传耗时
    pub upload_duration: Duration,
    /// 文件大小
    pub file_size: u64,
    /// 是否成功
    pub success: bool,
    /// 错误信息（如果失败）
    pub error: Option<String>,
}

/// CDN提供商接口
#[async_trait]
pub trait CdnProvider: Send + Sync {
    /// 上传文件到CDN
    async fn upload_file(&self, local_path: &Path, remote_path: &str) -> Result<UploadResult>;
    
    /// 删除CDN文件
    async fn delete_file(&self, remote_path: &str) -> Result<()>;
    
    /// 获取文件的CDN URL
    fn get_cdn_url(&self, remote_path: &str) -> String;
    
    /// 检查文件是否存在
    async fn file_exists(&self, remote_path: &str) -> Result<bool>;
    
    /// 获取提供商名称
    fn provider_name(&self) -> &'static str;
}

/// 本地CDN提供商（用于开发和测试）
pub struct LocalCdnProvider {
    base_path: String,
    base_url: String,
}

impl LocalCdnProvider {
    pub fn new(base_path: String, base_url: String) -> Self {
        Self { base_path, base_url }
    }
}

#[async_trait]
impl CdnProvider for LocalCdnProvider {
    async fn upload_file(&self, local_path: &Path, remote_path: &str) -> Result<UploadResult> {
        let start_time = std::time::Instant::now();
        
        let target_path = Path::new(&self.base_path).join(remote_path);
        
        // 确保目标目录存在
        if let Some(parent) = target_path.parent() {
            fs::create_dir_all(parent).await?;
        }
        
        // 复制文件
        fs::copy(local_path, &target_path).await?;
        
        let file_size = fs::metadata(local_path).await?.len();
        let upload_duration = start_time.elapsed();
        
        Ok(UploadResult {
            cdn_url: self.get_cdn_url(remote_path),
            upload_duration,
            file_size,
            success: true,
            error: None,
        })
    }
    
    async fn delete_file(&self, remote_path: &str) -> Result<()> {
        let target_path = Path::new(&self.base_path).join(remote_path);
        if target_path.exists() {
            fs::remove_file(target_path).await?;
        }
        Ok(())
    }
    
    fn get_cdn_url(&self, remote_path: &str) -> String {
        format!("{}/{}", self.base_url.trim_end_matches('/'), remote_path)
    }
    
    async fn file_exists(&self, remote_path: &str) -> Result<bool> {
        let target_path = Path::new(&self.base_path).join(remote_path);
        Ok(target_path.exists())
    }
    
    fn provider_name(&self) -> &'static str {
        "Local"
    }
}

/// CDN管理器
pub struct CdnManager {
    config: CdnConfig,
    provider: Arc<dyn CdnProvider>,
    upload_queue: Arc<RwLock<Vec<UploadTask>>>,
    upload_stats: Arc<RwLock<UploadStats>>,
}

/// 上传任务
#[derive(Debug, Clone)]
pub struct UploadTask {
    pub local_path: String,
    pub remote_path: String,
    pub priority: u8, // 0-255, 255最高优先级
    pub created_at: std::time::Instant,
    pub retry_count: u32,
}

/// 上传统计
#[derive(Debug, Default, Clone)]
pub struct UploadStats {
    pub total_uploads: u64,
    pub successful_uploads: u64,
    pub failed_uploads: u64,
    pub total_bytes: u64,
    pub average_upload_time: Duration,
    pub last_upload_time: Option<std::time::Instant>,
}

impl CdnManager {
    pub fn new(config: CdnConfig) -> Result<Self> {
        let provider: Arc<dyn CdnProvider> = match &config.provider {
            CdnProviderConfig::Local { base_path } => {
                Arc::new(LocalCdnProvider::new(
                    base_path.clone(),
                    "http://localhost:8080".to_string(), // 可配置
                ))
            }
            CdnProviderConfig::AliCloud { .. } => {
                return Err(anyhow!("AliCloud CDN provider not implemented yet"));
            }
            CdnProviderConfig::TencentCloud { .. } => {
                return Err(anyhow!("TencentCloud CDN provider not implemented yet"));
            }
            CdnProviderConfig::AWS { .. } => {
                return Err(anyhow!("AWS CDN provider not implemented yet"));
            }
            CdnProviderConfig::Custom { .. } => {
                return Err(anyhow!("Custom CDN provider not implemented yet"));
            }
        };

        Ok(Self {
            config,
            provider,
            upload_queue: Arc::new(RwLock::new(Vec::new())),
            upload_stats: Arc::new(RwLock::new(UploadStats::default())),
        })
    }

    /// 添加上传任务
    pub async fn queue_upload(&self, local_path: String, remote_path: String, priority: u8) -> Result<()> {
        if !self.config.enabled {
            return Ok(());
        }

        let task = UploadTask {
            local_path,
            remote_path,
            priority,
            created_at: std::time::Instant::now(),
            retry_count: 0,
        };

        let mut queue = self.upload_queue.write().await;
        queue.push(task);
        
        // 按优先级排序（高优先级在前）
        queue.sort_by(|a, b| b.priority.cmp(&a.priority));
        
        Ok(())
    }

    /// 处理上传队列
    pub async fn process_upload_queue(&self) -> Result<()> {
        if !self.config.enabled {
            return Ok(());
        }

        let mut tasks = {
            let mut queue = self.upload_queue.write().await;
            let tasks: Vec<_> = queue.drain(..).collect();
            tasks
        };

        if tasks.is_empty() {
            return Ok(());
        }

        log::info!("Processing {} CDN upload tasks", tasks.len());

        // 并发处理上传任务
        let semaphore = Arc::new(tokio::sync::Semaphore::new(self.config.upload_concurrency));
        let mut handles = Vec::new();

        for task in tasks {
            let semaphore = semaphore.clone();
            let provider = self.provider.clone();
            let config = self.config.clone();
            let stats = self.upload_stats.clone();

            let handle = tokio::spawn(async move {
                let _permit = semaphore.acquire().await.unwrap();
                Self::upload_with_retry(task, provider, config, stats).await
            });

            handles.push(handle);
        }

        // 等待所有上传完成
        for handle in handles {
            if let Err(e) = handle.await {
                log::error!("Upload task failed: {}", e);
            }
        }

        Ok(())
    }

    /// 带重试的上传
    async fn upload_with_retry(
        mut task: UploadTask,
        provider: Arc<dyn CdnProvider>,
        config: CdnConfig,
        stats: Arc<RwLock<UploadStats>>,
    ) -> Result<()> {
        let local_path = Path::new(&task.local_path);
        
        for attempt in 0..=config.retry_attempts {
            match tokio::time::timeout(
                Duration::from_secs(config.upload_timeout_secs),
                provider.upload_file(local_path, &task.remote_path)
            ).await {
                Ok(Ok(result)) => {
                    if result.success {
                        log::info!(
                            "Successfully uploaded {} to CDN: {} ({}ms, {} bytes)",
                            task.local_path,
                            result.cdn_url,
                            result.upload_duration.as_millis(),
                            result.file_size
                        );

                        // 更新统计
                        let mut stats = stats.write().await;
                        stats.total_uploads += 1;
                        stats.successful_uploads += 1;
                        stats.total_bytes += result.file_size;
                        stats.last_upload_time = Some(std::time::Instant::now());
                        
                        // 更新平均上传时间
                        let total_time = stats.average_upload_time.as_millis() as u64 * (stats.successful_uploads - 1)
                            + result.upload_duration.as_millis() as u64;
                        stats.average_upload_time = Duration::from_millis(total_time / stats.successful_uploads);

                        return Ok(());
                    } else {
                        log::warn!("Upload failed: {}", result.error.unwrap_or_default());
                    }
                }
                Ok(Err(e)) => {
                    log::warn!("Upload attempt {} failed: {}", attempt + 1, e);
                }
                Err(_) => {
                    log::warn!("Upload attempt {} timed out", attempt + 1);
                }
            }

            if attempt < config.retry_attempts {
                tokio::time::sleep(Duration::from_secs(config.retry_delay_secs)).await;
                task.retry_count += 1;
            }
        }

        // 所有重试都失败了
        let mut stats = stats.write().await;
        stats.total_uploads += 1;
        stats.failed_uploads += 1;
        
        log::error!("Failed to upload {} after {} attempts", task.local_path, config.retry_attempts + 1);
        Ok(())
    }

    /// 获取上传统计
    pub async fn get_upload_stats(&self) -> UploadStats {
        self.upload_stats.read().await.clone()
    }

    /// 获取CDN URL
    pub fn get_cdn_url(&self, remote_path: &str) -> String {
        self.provider.get_cdn_url(remote_path)
    }

    /// 清理本地缓存文件
    pub async fn cleanup_local_cache(&self, local_path: &Path) -> Result<()> {
        if self.config.local_cache {
            return Ok(()); // 保留本地缓存
        }

        if local_path.exists() {
            fs::remove_file(local_path).await?;
            log::debug!("Cleaned up local file: {}", local_path.display());
        }

        Ok(())
    }
}

// 全局CDN管理器
use std::sync::OnceLock;
static GLOBAL_CDN_MANAGER: OnceLock<Arc<CdnManager>> = OnceLock::new();

pub fn init_global_cdn_manager(config: CdnConfig) -> Result<()> {
    let manager = Arc::new(CdnManager::new(config)?);
    GLOBAL_CDN_MANAGER.set(manager).map_err(|_| anyhow!("CDN manager already initialized"))?;
    Ok(())
}

pub fn get_global_cdn_manager() -> Option<Arc<CdnManager>> {
    GLOBAL_CDN_MANAGER.get().cloned()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_cdn_config_default() {
        let config = CdnConfig::default();
        assert!(!config.enabled);
        assert_eq!(config.upload_concurrency, 4);
        assert_eq!(config.retry_attempts, 3);
    }

    #[test]
    fn test_local_cdn_provider_url() {
        let provider = LocalCdnProvider::new(
            "/tmp/cdn".to_string(),
            "http://localhost:8080".to_string()
        );

        let url = provider.get_cdn_url("streams/test.txt");
        assert_eq!(url, "http://localhost:8080/streams/test.txt");
        assert_eq!(provider.provider_name(), "Local");
    }

    #[tokio::test]
    async fn test_upload_stats() {
        let config = CdnConfig::default();
        let manager = CdnManager::new(config).unwrap();

        let stats = manager.get_upload_stats().await;
        assert_eq!(stats.total_uploads, 0);
        assert_eq!(stats.successful_uploads, 0);
        assert_eq!(stats.failed_uploads, 0);
    }
}
